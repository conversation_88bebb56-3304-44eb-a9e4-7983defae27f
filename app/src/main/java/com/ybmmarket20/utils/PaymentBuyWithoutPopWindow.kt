package com.ybmmarket20.utils

import android.annotation.SuppressLint
import android.content.Context
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.gson.Gson
import com.ybmmarket20.R
import com.ybmmarket20.adapter.RecommendPayWithoutAdapter
import com.ybmmarket20.bean.BuySomethingCasuallyInfo
import com.ybmmarket20.bean.PaymentSuiXinPinSkusItemBean
import com.ybmmarket20.bean.RecommendShunShouMaiBean
import com.ybmmarket20.bean.ShopInfoSxpList
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.util.ConvertUtils
import com.ybmmarket20.view.BaseBottomPopWindow
import com.ybmmarket20.viewmodel.SpellGroupRecommendGoodsViewModel
import java.math.BigDecimal

/**
 * 顺手买底部弹框
 */
class PaymentBuyWithoutPopWindow(
    val mContext: Context,
    val callback: (MutableList<RecommendShunShouMaiBean>) -> Unit
) : BaseBottomPopWindow() {
    private var mAdapter: RecommendPayWithoutAdapter? = null
    private var goodsList: MutableList<PaymentSuiXinPinSkusItemBean>? = null
    private var mShopCode: String? = null
    private var addedSuiXinPinGoodsList = mutableListOf<RecommendShunShouMaiBean>()

    private var mViewModel: SpellGroupRecommendGoodsViewModel? = null
    private var mKeyword: String = ""

    //选品id集合
    private var mBuySomethingCasuallySkusList: ArrayList<String>? = null
    private var mShopInfoSxp: ArrayList<ShopInfoSxpList>? = null
    private var skuCount: TextView? = null
    private var totalPrice: TextView? = null
    private var cartCount: TextView? = null
    var mPopWindowDismiss: (() -> Unit)? = null
    override fun getLayoutId(): Int = R.layout.popwindow_recommend_sui_xin_pin

    init {
        if (goodsList == null) {
            goodsList = mutableListOf()
        }
    }

    override fun initView() {
    }

    fun setData(
        buySomethingCasuallySkusList: ArrayList<String>,
        shopInfoSxp: ArrayList<ShopInfoSxpList>,
        viewModel: SpellGroupRecommendGoodsViewModel
    ) {
        mViewModel = viewModel
        mBuySomethingCasuallySkusList = buySomethingCasuallySkusList
        mShopInfoSxp = shopInfoSxp
        mShopCode = viewModel.shopCode
        val rv = getView<RecyclerView>(R.id.rv)
        val ivClose = getView<ImageView>(R.id.ivClose)
        cartCount = getView<TextView>(R.id.tv_cart_count_text)
        skuCount = getView<TextView>(R.id.tv_cart_count)
        totalPrice = getView<TextView>(R.id.tv_cart_price)
        val submit = getView<TextView>(R.id.tv_settle)
        val tips = getView<TextView>(R.id.tv_cart_tips)
        val etSearch = getView<TextView>(R.id.et_search)
        val ivEtClear = getView<ImageView>(R.id.iv_et_clear)
        val tvTitle = getView<TextView>(R.id.tvTitle)
        tvTitle.setText("顺手买一件")
        submit.setOnClickListener {
            dismiss()
        }
        tips.visibility=View.GONE
        ivEtClear.setOnClickListener {
            etSearch.setText("")
            mAdapter?.setNewData(goodsList)
            if (goodsList?.isEmpty() == true) {
                mAdapter?.setEmptyView(
                    mContext,
                    R.layout.layout_search_product_empty_view,
                    R.drawable.icon_empty,
                    "啊哦...\n没有找到相关的商品"
                )
            }
        }

        etSearch.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }

            override fun afterTextChanged(s: Editable?) {
                mKeyword = s.toString()
                ivEtClear.visibility = if (s.toString().isEmpty()) View.GONE else View.VISIBLE
                if (TextUtils.isEmpty(mKeyword)) {
                    mAdapter?.setNewData(goodsList)
                    if (goodsList?.isEmpty() == true) {
                        mAdapter?.setEmptyView(
                            mContext,
                            R.layout.layout_search_product_empty_view,
                            R.drawable.icon_empty,
                            "啊哦...\n没有找到相关的商品"
                        )
                    }
                }
            }
        })

        etSearch.imeOptions = EditorInfo.IME_ACTION_SEARCH
        etSearch.setOnEditorActionListener { v, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                if (mContext is BaseActivity) { //隐藏键盘
                    mContext.hideSoftInput(etSearch)
                }
                if (mKeyword.isNotEmpty()) {
                   getSearchData()
                } else {
                    mAdapter?.setNewData(goodsList)
                    if (goodsList?.isEmpty() == true) {
                        mAdapter?.setEmptyView(
                            mContext,
                            R.layout.layout_search_product_empty_view,
                            R.drawable.icon_empty,
                            "啊哦...\n没有找到相关的商品"
                        )
                    }
                }
                return@setOnEditorActionListener true

            }
            return@setOnEditorActionListener false
        }


        ivClose.setOnClickListener { dismiss() }
        goodsList = mutableListOf()
        mAdapter = RecommendPayWithoutAdapter(goodsList!!, { recommendSuiXinPinBean ->
            setPriceData(recommendSuiXinPinBean)
        }, viewModel)

        rv.layoutManager = LinearLayoutManager(mContext, LinearLayoutManager.VERTICAL, false)
        rv.adapter = mAdapter
        getNewGoodsList()

        initObserver()
    }

    fun setPriceData(dataBean: RecommendShunShouMaiBean) {
        if (addedSuiXinPinGoodsList != null) {
            val bean = addedSuiXinPinGoodsList.find { it.id == dataBean.id }
            if (bean != null) {
                addedSuiXinPinGoodsList.remove(bean)
            }
        }
        addedSuiXinPinGoodsList.add(dataBean)
        var goodsCount = 0
        var price = BigDecimal(0)
        addedSuiXinPinGoodsList.forEach {
            val selectedCount = it.count
            goodsCount += selectedCount
            price = price.add(
                BigDecimal(it.price?:"").multiply(
                    BigDecimal(
                        selectedCount
                    )
                )
            )
        }
        skuCount?.text =
            if (addedSuiXinPinGoodsList.size > 99) "99+" else "${addedSuiXinPinGoodsList.count { it.count != 0 }}"
        cartCount?.text = "共${goodsCount}件"

        totalPrice?.text = "${price.toDouble()}"
    }

    @SuppressLint("NotifyDataSetChanged")
    fun initObserver() {
        mViewModel?.changeCartForPromotionBeanLiveData?.observe(
            mContext as BaseActivity
        ) { dataBean ->
            mContext.dismissProgress()
            if (dataBean.isSuccess) {
                var bean: PaymentSuiXinPinSkusItemBean? = null
                goodsList?.forEach {
                    if (it.id == dataBean.data.skuId) {
                        it.qty = dataBean.data.qty
                        bean = it
                    }
                }
                if (!bean?.id.isNullOrEmpty()&&!bean?.price.isNullOrEmpty()){
                setPriceData(
                    RecommendShunShouMaiBean(
                        bean?.id.toString(), bean?.price, bean?.qty ?: 0
                    )
                )}
                getSearchData()
                if (TextUtils.isEmpty(mKeyword)) {
                    mAdapter?.notifyDataSetChanged()
                }
            }
        }
        mViewModel?.buySomethingBeanPopLiveData?.observe(mContext as BaseActivity) { dataBean ->
            mContext.dismissProgress()
            mAdapter?.setNewData(dataBean.getData())
            if (dataBean.isSuccess() && dataBean.data != null && dataBean.data.isNotEmpty()) {
                goodsList?.clear()
                goodsList?.addAll(dataBean.getData())
            } else {
                mAdapter?.setEmptyView(
                    mContext,
                    R.layout.layout_search_product_empty_view,
                    R.drawable.icon_empty,
                    "啊哦...\n没有找到相关的商品"
                )
            }
        }
    }
    override fun show(token: View?) {
        super.show(token)
        popwindow.setOnDismissListener {
            if (addedSuiXinPinGoodsList.isEmpty()) {
                return@setOnDismissListener
            }
            mViewModel?.clearWithoutGoods()
            addedSuiXinPinGoodsList.filter { it.count > 0 }.toMutableList().let(callback::invoke)
            mPopWindowDismiss?.invoke()
        }
    }

    override fun dismiss() {
        super.dismiss()
        mViewModel?.clearBuySomethingPopData()
    }

    private fun getSearchData(){
        if (!TextUtils.isEmpty(mKeyword)) {
            val newGoodsList = ArrayList<PaymentSuiXinPinSkusItemBean>()
            if (goodsList != null) {
                for (good in goodsList!!) {
                    if (good.showName?.contains(mKeyword) == true) {
                        newGoodsList.add(good)
                    }
                }
            }
            if (newGoodsList.isNotEmpty()) {
                mAdapter?.setNewData(newGoodsList)
            } else {
                mAdapter?.setNewData(newGoodsList)
                mAdapter?.setEmptyView(
                    mContext,
                    R.layout.layout_search_product_empty_view,
                    R.drawable.icon_empty,
                    "啊哦...\n没有找到相关的商品"
                )
            }
        }
    }
    private fun getNewGoodsList() {
        if (mContext is BaseActivity) {
            mContext.showProgress()
        }
        val buySomethingCasuallyInfo =
            BuySomethingCasuallyInfo(mShopInfoSxp, mBuySomethingCasuallySkusList, true)
        val json = Gson().toJson(buySomethingCasuallyInfo)
        mViewModel?.getBuySomethingPopData(json)
    }


    override fun getLayoutParams(): LinearLayout.LayoutParams {
        return LinearLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            UiUtils.getScreenHeight() - ConvertUtils.dp2px(60f)
        );
    }
}